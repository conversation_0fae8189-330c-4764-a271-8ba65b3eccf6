import React, { useState, useEffect } from "react";
import { fetchHotelAddOns, type HotelAddOn } from "../../utils/store/hotels";

interface AddOnSelection {
  service_id: string;
  adult_quantity: number;
  child_quantity: number;
  total_price: number;
}

interface AddOnsProps {
  hotelId: string;
  adults: number;
  children: number;
  currencyCode: string;
  onAddOnsChange: (addOns: AddOnSelection[]) => void;
}

const AddOns: React.FC<AddOnsProps> = ({
  hotelId,
  adults,
  children, // Keep for interface consistency, children selector now based on add-on pricing
  currencyCode,
  onAddOnsChange,
}) => {
  const [addOns, setAddOns] = useState<HotelAddOn[]>([]);
  const [selectedAddOns, setSelectedAddOns] = useState<AddOnSelection[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch add-ons when component mounts
  useEffect(() => {
    const loadAddOns = async () => {
      try {
        setLoading(true);
        const { add_ons } = await fetchHotelAddOns(hotelId);
        // Filter add-ons that are available for this hotel
        const filteredAddOns = add_ons.filter((addon) => {
          // Check if hotel_id is an array and includes the current hotel
          if (addon.hotel_id && Array.isArray(addon.hotel_id)) {
            return addon.hotel_id.includes(hotelId);
          }
          // If hotel_id is null, it might be a destination-level add-on
          // We can include these as well since they're returned by the API
          return addon.hotel_id === null;
        });
        setAddOns(filteredAddOns);
      } catch (err) {
        console.error("Error loading add-ons:", err);
        setError("Failed to load add-ons");
      } finally {
        setLoading(false);
      }
    };

    if (hotelId) {
      loadAddOns();
    }
  }, [hotelId]);

  // Notify parent component when selections change
  useEffect(() => {
    onAddOnsChange(selectedAddOns);
  }, [selectedAddOns, onAddOnsChange]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleQuantityChange = (
    serviceId: string,
    type: "adult" | "child",
    quantity: number
  ) => {
    const addOn = addOns.find((a) => a.id === serviceId);
    if (!addOn) return;

    setSelectedAddOns((prev) => {
      const existing = prev.find((s) => s.service_id === serviceId);

      if (existing) {
        // Update existing selection
        const updated = {
          ...existing,
          [type === "adult" ? "adult_quantity" : "child_quantity"]: quantity,
        };

        // Check max capacity constraint
        const totalQuantity = updated.adult_quantity + updated.child_quantity;
        if (totalQuantity > addOn.max_capacity) {
          return prev; // Don't update if it exceeds max capacity
        }

        // Calculate total price
        updated.total_price =
          updated.adult_quantity * addOn.adult_price +
          updated.child_quantity * addOn.child_price;

        // Remove if both quantities are 0
        if (updated.adult_quantity === 0 && updated.child_quantity === 0) {
          return prev.filter((s) => s.service_id !== serviceId);
        }

        return prev.map((s) => (s.service_id === serviceId ? updated : s));
      } else {
        // Create new selection
        if (quantity === 0) return prev; // Don't add if quantity is 0

        // Check max capacity constraint
        if (quantity > addOn.max_capacity) {
          return prev; // Don't add if it exceeds max capacity
        }

        const newSelection: AddOnSelection = {
          service_id: serviceId,
          adult_quantity: type === "adult" ? quantity : 0,
          child_quantity: type === "child" ? quantity : 0,
          total_price:
            type === "adult"
              ? quantity * addOn.adult_price
              : quantity * addOn.child_price,
        };

        return [...prev, newSelection];
      }
    });
  };

  const getSelectedQuantity = (
    serviceId: string,
    type: "adult" | "child"
  ): number => {
    const selection = selectedAddOns.find((s) => s.service_id === serviceId);
    return selection
      ? selection[type === "adult" ? "adult_quantity" : "child_quantity"]
      : 0;
  };

  const getTotalSelectedQuantity = (serviceId: string): number => {
    const selection = selectedAddOns.find((s) => s.service_id === serviceId);
    return selection ? selection.adult_quantity + selection.child_quantity : 0;
  };

  const getRemainingCapacity = (serviceId: string): number => {
    const addOn = addOns.find((a) => a.id === serviceId);
    if (!addOn) return 0;
    return addOn.max_capacity - getTotalSelectedQuantity(serviceId);
  };

  const canIncreaseQuantity = (
    serviceId: string,
    type: "adult" | "child"
  ): boolean => {
    const addOn = addOns.find((a) => a.id === serviceId);
    if (!addOn) return false;

    const currentQuantity = getSelectedQuantity(serviceId, type);
    const remainingCapacity = getRemainingCapacity(serviceId);

    // For adults, limit to the number of adults in the booking
    // For children, allow unlimited (up to remaining capacity) since add-ons can include children even if booking doesn't
    if (type === "adult") {
      return currentQuantity < adults && remainingCapacity > 0;
    } else {
      // For children, only limit by remaining capacity and if the add-on has child pricing
      return addOn.child_price > 0 && remainingCapacity > 0;
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Add-ons & Services
        </h3>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Add-ons & Services
        </h3>
        <p className="text-red-600 text-sm">{error}</p>
      </div>
    );
  }

  if (addOns.length === 0) {
    return null; // Don't show the section if no add-ons are available
  }

  return (
    <div className="bg-gradient-to-br from-white to-blue-50/30 rounded-xl border border-blue-100 shadow-sm p-6">
      <div className="flex items-center mb-6">
        <div className="w-10 h-10 bg-gradient-to-br from-[#3566ab] to-blue-600 rounded-lg flex items-center justify-center mr-3">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-white"
          >
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M12 1v6m0 6v6"></path>
            <path d="m21 12-6 0m-6 0-6 0"></path>
          </svg>
        </div>
        <div>
          <h3 className="text-xl font-bold text-gray-900">
            Add-ons & Services
          </h3>
          <p className="text-sm text-gray-600">
            Enhance your stay with our additional services
          </p>
        </div>
      </div>

      <div className="space-y-4">
        {addOns.map((addOn) => {
          const totalSelected = getTotalSelectedQuantity(addOn.id);
          const remainingCapacity = getRemainingCapacity(addOn.id);
          const isSelected = totalSelected > 0;

          return (
            <div
              key={addOn.id}
              className={`relative overflow-hidden rounded-xl border-2 transition-all duration-300 ${
                isSelected
                  ? "border-[#3566ab] bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md"
                  : "border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm"
              }`}
            >
              {/* Service Header */}
              <div className="p-5">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <h4 className="text-lg font-semibold text-gray-900 mr-3">
                        {addOn.name}
                      </h4>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {addOn.service_type}
                      </span>
                    </div>

                    {addOn.description && (
                      <p className="text-sm text-gray-600 mb-3">
                        {addOn.description}
                      </p>
                    )}

                    {/* Pricing Display */}
                    <div className="flex flex-wrap gap-4 mb-3">
                      <div className="flex items-center bg-white rounded-lg px-3 py-2 border border-gray-200">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-blue-600 mr-2"
                        >
                          <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                          <circle cx="9" cy="7" r="4"></circle>
                          <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span className="text-sm font-medium text-gray-700">
                          Adults: {formatCurrency(addOn.adult_price)}
                        </span>
                      </div>

                      {addOn.child_price > 0 && (
                        <div className="flex items-center bg-white rounded-lg px-3 py-2 border border-gray-200">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-green-600 mr-2"
                          >
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                          </svg>
                          <span className="text-sm font-medium text-gray-700">
                            Children: {formatCurrency(addOn.child_price)}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Capacity Info */}
                    <div className="flex items-center text-xs text-gray-500 mb-4">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="mr-1"
                      >
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 8v4"></path>
                        <path d="M12 16h.01"></path>
                      </svg>
                      Max capacity: {addOn.max_capacity} people
                      {totalSelected > 0 && (
                        <span className="ml-2 text-blue-600 font-medium">
                          • {remainingCapacity} remaining
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Service Image */}
                  {addOn.images && addOn.images.length > 0 && (
                    <div className="ml-4">
                      <img
                        src={addOn.images[0]}
                        alt={addOn.name}
                        className="w-20 h-20 object-cover rounded-xl border-2 border-white shadow-sm"
                      />
                    </div>
                  )}
                </div>

                {/* Quantity Selectors */}
                <div className="flex flex-wrap gap-4">
                  {/* Adult Selector */}
                  <div className="flex items-center bg-white rounded-lg border border-gray-200 p-1">
                    <span className="text-sm font-medium text-gray-700 px-3">
                      Adults
                    </span>
                    <div className="flex items-center">
                      <button
                        type="button"
                        onClick={() =>
                          handleQuantityChange(
                            addOn.id,
                            "adult",
                            Math.max(
                              0,
                              getSelectedQuantity(addOn.id, "adult") - 1
                            )
                          )
                        }
                        disabled={getSelectedQuantity(addOn.id, "adult") === 0}
                        className="w-8 h-8 rounded-lg flex items-center justify-center text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M5 12h14"></path>
                        </svg>
                      </button>
                      <span className="w-8 text-center text-sm font-semibold text-gray-900">
                        {getSelectedQuantity(addOn.id, "adult")}
                      </span>
                      <button
                        type="button"
                        onClick={() =>
                          handleQuantityChange(
                            addOn.id,
                            "adult",
                            getSelectedQuantity(addOn.id, "adult") + 1
                          )
                        }
                        disabled={!canIncreaseQuantity(addOn.id, "adult")}
                        className="w-8 h-8 rounded-lg flex items-center justify-center text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M12 5v14"></path>
                          <path d="M5 12h14"></path>
                        </svg>
                      </button>
                    </div>
                  </div>

                  {/* Children Selector - Show if add-on has child pricing */}
                  {addOn.child_price > 0 && (
                    <div className="flex items-center bg-white rounded-lg border border-gray-200 p-1">
                      <span className="text-sm font-medium text-gray-700 px-3">
                        Children
                      </span>
                      <div className="flex items-center">
                        <button
                          type="button"
                          onClick={() =>
                            handleQuantityChange(
                              addOn.id,
                              "child",
                              Math.max(
                                0,
                                getSelectedQuantity(addOn.id, "child") - 1
                              )
                            )
                          }
                          disabled={
                            getSelectedQuantity(addOn.id, "child") === 0
                          }
                          className="w-8 h-8 rounded-lg flex items-center justify-center text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M5 12h14"></path>
                          </svg>
                        </button>
                        <span className="w-8 text-center text-sm font-semibold text-gray-900">
                          {getSelectedQuantity(addOn.id, "child")}
                        </span>
                        <button
                          type="button"
                          onClick={() =>
                            handleQuantityChange(
                              addOn.id,
                              "child",
                              getSelectedQuantity(addOn.id, "child") + 1
                            )
                          }
                          disabled={!canIncreaseQuantity(addOn.id, "child")}
                          className="w-8 h-8 rounded-lg flex items-center justify-center text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M12 5v14"></path>
                            <path d="M5 12h14"></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Subtotal for this add-on */}
                {isSelected && (
                  <div className="mt-4 pt-4 border-t border-blue-200">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center text-sm text-gray-600">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-2"
                        >
                          <path d="M12 2v20"></path>
                          <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                        </svg>
                        Subtotal ({totalSelected}{" "}
                        {totalSelected === 1 ? "person" : "people"})
                      </div>
                      <span className="text-lg font-bold text-[#3566ab]">
                        {formatCurrency(
                          selectedAddOns.find((s) => s.service_id === addOn.id)
                            ?.total_price || 0
                        )}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute top-4 right-4">
                  <div className="w-6 h-6 bg-[#3566ab] rounded-full flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="3"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-white"
                    >
                      <path d="M20 6L9 17l-5-5"></path>
                    </svg>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AddOns;

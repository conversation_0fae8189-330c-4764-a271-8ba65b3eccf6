---
import { getLangFromUrl, useTranslations } from "../../i18n/utils";

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<!-- Contact Form -->
<div>
  <h2 class="text-2xl font-baskervville mb-3">{t("contact.form.title")}</h2>

  <div
    id="form-success"
    class="hidden bg-primary/10 border border-primary rounded-lg p-4 text-center mb-4"
  >
    <h3 class="text-lg font-baskervville mb-1">
      {t("contact.form.successTitle")}
    </h3>
    <p class="text-sm">
      {t("contact.form.successMessage")}
    </p>
  </div>

  <form id="contact-form" class="space-y-5">
    <!-- Travel Type Dropdown -->
    <div>
      <label
        for="travel-type"
        class="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
        >{t("contact.form.travelType")}</label
      >
      <div class="relative">
        <select
          id="travel-type"
          name="travel-type"
          class="w-full appearance-none px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200 pr-8"
        >
          <option value="" disabled selected
            >{t("contact.form.travelTypePlaceholder")}</option
          >
          <option value="ski">{t("contact.form.travelTypeOptions.ski")}</option>
          <option value="luxury"
            >{t("contact.form.travelTypeOptions.luxury")}</option
          >
          <option value="family"
            >{t("contact.form.travelTypeOptions.family")}</option
          >
          <option value="adventure"
            >{t("contact.form.travelTypeOptions.adventure")}</option
          >
          <option value="other"
            >{t("contact.form.travelTypeOptions.other")}</option
          >
        </select>
        <div
          class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-[#285DA6]"
        >
          <svg
            class="fill-current h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
          >
            <path
              d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"
            ></path>
          </svg>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label
          for="name"
          class="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
          >{t("contact.form.name")}</label
        >
        <input
          type="text"
          id="name"
          name="name"
          placeholder={t("contact.form.namePlaceholder")}
          class="w-full px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200"
          required
        />
      </div>

      <div>
        <label
          for="email"
          class="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
          >{t("contact.form.email")}</label
        >
        <input
          type="email"
          id="email"
          name="email"
          placeholder={t("contact.form.emailPlaceholder")}
          class="w-full px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200"
          required
        />
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label
          for="phone"
          class="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
          >{t("contact.form.phone")}</label
        >
        <input
          type="tel"
          id="phone"
          name="phone"
          placeholder={t("contact.form.phonePlaceholder")}
          class="w-full px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200"
        />
      </div>

      <div>
        <label
          for="travel-dates"
          class="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
          >{t("contact.form.travelDates")}</label
        >
        <input
          type="text"
          id="travel-dates"
          name="travel-dates"
          placeholder={t("contact.form.travelDatesPlaceholder")}
          class="w-full px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200"
        />
      </div>
    </div>

    <div>
      <label
        for="message"
        class="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
        >{t("contact.form.message")}</label
      >
      <textarea
        id="message"
        name="message"
        rows="4"
        placeholder={t("contact.form.messagePlaceholder")}
        class="w-full px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200 resize-none"
        required></textarea>
    </div>

    <div>
      <label
        class="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-2"
        >{t("contact.form.preferredContact")}</label
      >
      <div class="flex space-x-6">
        <label class="flex items-center">
          <input
            type="radio"
            name="preferredContact"
            value="email"
            checked
            class="mr-2 h-4 w-4 text-[#285DA6] focus:ring-[#285DA6]"
          />
          <span class="text-sm">{t("contact.form.preferredContactEmail")}</span>
        </label>

        <label class="flex items-center">
          <input
            type="radio"
            name="preferredContact"
            value="phone"
            class="mr-2 h-4 w-4 text-[#285DA6] focus:ring-[#285DA6]"
          />
          <span class="text-sm">{t("contact.form.preferredContactPhone")}</span>
        </label>
      </div>
    </div>

    <!-- Privacy Policy Checkbox -->
    <div class="mt-4">
      <label class="flex items-start">
        <input
          type="checkbox"
          name="privacy-policy"
          required
          class="mt-0.5 mr-2 h-5 w-5 text-[#285DA6] focus:ring-[#285DA6] rounded"
        />
        <span class="text-sm">
          {t("contact.form.privacyPolicyPrefix")}
          <a href="/privacy-policy" class="text-[#285DA6] hover:underline"
            >{t("contact.form.privacyPolicyLink")}</a
          >
          {t("contact.form.privacyPolicyAnd")}
          <a href="/booking-conditions" class="text-[#285DA6] hover:underline"
            >{t("contact.form.bookingConditionsLink")}</a
          >.
        </span>
      </label>
    </div>

    <div class="pt-4">
      <button
        type="submit"
        class="inline-flex items-center justify-center px-10 py-3 bg-[#285DA6] text-white rounded-md hover:bg-[#285DA6]/90 transition-all duration-200 text-sm font-medium"
      >
        {t("contact.form.submit")}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </button>
    </div>
  </form>
</div>

<script>
  // Handle form submission
  document.addEventListener("DOMContentLoaded", () => {
    const form = document.getElementById("contact-form") as HTMLFormElement;
    const formSuccess = document.getElementById("form-success");

    if (form && formSuccess) {
      form.addEventListener("submit", (e) => {
        e.preventDefault();

        // Show success message
        form.classList.add("hidden");
        formSuccess.classList.remove("hidden");

        // Reset form after submission
        setTimeout(() => {
          form.reset();
          form.classList.remove("hidden");
          formSuccess.classList.add("hidden");
        }, 5000);
      });
    }
  });
</script>

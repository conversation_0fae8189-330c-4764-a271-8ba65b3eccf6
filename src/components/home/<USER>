import React, { useState, useEffect, useRef } from "react";
import "../../styles/video-hero.css";
import "../../styles/ai-search-hero.css";
import ExpandableHeaderSearch from "../search/ExpandableHeaderSearch";

interface VideoSource {
  src: string;
  type: string;
}

interface VideoHeroProps {
  title: string;
  subtitle: string;
  videoSources?: VideoSource[];
  fallbackImage?: string;
  destinations?: string[];
}

const VideoHeroSection: React.FC<VideoHeroProps> = ({
  title,
  subtitle,
  videoSources = [
    {
      src: "/videos/hero/skiing-1.mp4",
      type: "video/mp4",
    },
  ],
  fallbackImage = "/images/st-anton-powder-skiing.jpg",
  destinations = ["Zermatt", "St. Moritz", "Verbier", "Courchevel", "Aspen"],
}) => {
  const [currentDestination, setCurrentDestination] = useState(0);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [activeVideoIndex, setActiveVideoIndex] = useState(0); // 0 or 1 for dual video setup
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [videosLoaded, setVideosLoaded] = useState([false, false]);
  const [isVideoPlaying, setIsVideoPlaying] = useState(true);
  const videoRefs = [
    useRef<HTMLVideoElement>(null),
    useRef<HTMLVideoElement>(null),
  ];

  // Smooth video transition function
  const transitionToVideo = async (newVideoIndex: number) => {
    if (newVideoIndex === currentVideoIndex || isTransitioning) return;

    setIsTransitioning(true);
    const nextVideoIndex = activeVideoIndex === 0 ? 1 : 0;
    const nextVideo = videoRefs[nextVideoIndex].current;

    if (nextVideo) {
      // Set the new video source
      nextVideo.src = videoSources[newVideoIndex]?.src || "";

      // Preload the new video
      try {
        await new Promise((resolve, reject) => {
          const handleCanPlay = () => {
            nextVideo.removeEventListener("canplay", handleCanPlay);
            nextVideo.removeEventListener("error", handleError);
            resolve(void 0);
          };

          const handleError = () => {
            nextVideo.removeEventListener("canplay", handleCanPlay);
            nextVideo.removeEventListener("error", handleError);
            reject(new Error("Video failed to load"));
          };

          nextVideo.addEventListener("canplay", handleCanPlay);
          nextVideo.addEventListener("error", handleError);
          nextVideo.load();
        });

        // Start playing the new video
        await nextVideo.play();

        // Switch active video index for smooth transition
        setActiveVideoIndex(nextVideoIndex);
        setCurrentVideoIndex(newVideoIndex);

        // Update loaded state
        setVideosLoaded((prev) => {
          const newState = [...prev];
          newState[nextVideoIndex] = true;
          return newState;
        });
      } catch (error) {
        console.warn("Failed to load video:", error);
      }
    }

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 800);
  };

  // Change destination and video every 8 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      const nextDestination = (currentDestination + 1) % destinations.length;
      setCurrentDestination(nextDestination);

      // Only change video if we have more than one video source
      if (videoSources.length > 1) {
        const nextVideoIndex = (currentVideoIndex + 1) % videoSources.length;
        transitionToVideo(nextVideoIndex);
      }
    }, 8000);

    return () => clearInterval(interval);
  }, [
    currentDestination,
    currentVideoIndex,
    destinations.length,
    videoSources.length,
    activeVideoIndex,
    isTransitioning,
  ]);

  // Handle video loading for individual videos
  const handleVideoLoaded = (videoIndex: number) => {
    console.log(
      `Video ${videoIndex} loaded successfully! Source:`,
      videoSources[currentVideoIndex]?.src
    );
    setVideosLoaded((prev) => {
      const newState = [...prev];
      newState[videoIndex] = true;
      return newState;
    });
  };

  // Handle video error
  const handleVideoError = (
    e: React.SyntheticEvent<HTMLVideoElement, Event>,
    videoIndex: number
  ) => {
    console.warn(
      `Video ${videoIndex} could not be loaded. Using fallback image instead.`,
      e
    );
    console.error(
      "Video source that failed:",
      videoSources[currentVideoIndex]?.src
    );
    setVideosLoaded((prev) => {
      const newState = [...prev];
      newState[videoIndex] = false;
      return newState;
    });
  };

  // Initialize videos on component mount
  useEffect(() => {
    console.log("VideoHeroSection mounted with video sources:", videoSources);

    // Initialize the first video
    const firstVideo = videoRefs[0].current;
    if (firstVideo && videoSources[0]) {
      firstVideo.src = videoSources[0].src;
      firstVideo.load();

      // Set initial video as loaded after a short delay
      const timer = setTimeout(() => {
        console.log("Setting initial video to loaded state");
        setVideosLoaded((prev) => {
          const newState = [...prev];
          newState[0] = true;
          return newState;
        });
      }, 300);

      return () => clearTimeout(timer);
    }
  }, []);

  // Toggle video play/pause
  const toggleVideoPlay = () => {
    const activeVideo = videoRefs[activeVideoIndex].current;
    if (activeVideo) {
      if (isVideoPlaying) {
        activeVideo.pause();
      } else {
        activeVideo.play();
      }
      setIsVideoPlaying(!isVideoPlaying);
    }
  };

  // Handle destination dot click
  const handleDestinationClick = (index: number) => {
    console.log(`Clicked on destination ${index}: ${destinations[index]}`);

    // Update current destination
    setCurrentDestination(index);

    // Update video index if we have multiple videos
    if (videoSources.length > 1) {
      let newVideoIndex;
      // If we have the same number of videos as destinations, match them directly
      if (videoSources.length === destinations.length) {
        newVideoIndex = index;
      } else {
        // Otherwise, use modulo to cycle through available videos
        newVideoIndex = index % videoSources.length;
      }

      // Use smooth transition
      transitionToVideo(newVideoIndex);
    }
  };

  return (
    <div className="hero-section-container">
      <section
        className="relative overflow-hidden hero-section"
        aria-label="Welcome to our collection of luxury ski destinations"
      >
        {/* Video Background - Dual Video Setup for Smooth Transitions */}
        <div className="video-container">
          {/* Video 1 */}
          <video
            ref={videoRefs[0]}
            id="hero-video-1"
            autoPlay
            muted
            loop
            playsInline
            controls={false}
            preload="auto"
            onLoadedData={() => handleVideoLoaded(0)}
            onError={(e) => handleVideoError(e, 0)}
            onPlay={() => console.log("Video 1 started playing")}
            className={`video-element ${
              activeVideoIndex === 0 && videosLoaded[0]
                ? "video-active"
                : "video-inactive"
            }`}
          >
            {/* Source will be set dynamically via ref */}
            Your browser does not support the video tag.
          </video>

          {/* Video 2 */}
          <video
            ref={videoRefs[1]}
            id="hero-video-2"
            autoPlay
            muted
            loop
            playsInline
            controls={false}
            preload="auto"
            onLoadedData={() => handleVideoLoaded(1)}
            onError={(e) => handleVideoError(e, 1)}
            onPlay={() => console.log("Video 2 started playing")}
            className={`video-element ${
              activeVideoIndex === 1 && videosLoaded[1]
                ? "video-active"
                : "video-inactive"
            }`}
          >
            {/* Source will be set dynamically via ref */}
            Your browser does not support the video tag.
          </video>

          {/* Dark overlay */}
          <div className="video-overlay"></div>
        </div>

        {/* Content Container */}
        <div className="content-container">
          {/* Hero Content */}
          <div className="hero-content">
            <h1 className="hero-title">
              {(() => {
                const words = title.split(" ");
                if (words.length >= 2) {
                  const firstPart = words.slice(0, -2).join(" ");
                  const lastTwoWords = words.slice(-2).join(" ");
                  return (
                    <>
                      {firstPart && `${firstPart} `}
                      <span className="luxury-accent">{lastTwoWords}</span>
                    </>
                  );
                }
                return title;
              })()}
            </h1>
            <p className="hero-subtitle">{subtitle}</p>
            <div className="hero-search-container">
              <ExpandableHeaderSearch isExpandedView={true} />
            </div>
          </div>
        </div>

        {/* Controls Container - Positioned absolutely on top of everything */}
        <div
          className="controls-container"
          style={{
            position: "absolute",
            bottom: 0,
            left: 0,
            right: 0,
            // zIndex: 100,
            pointerEvents: "none",
          }}
        >
          {/* Video Controls - Now positioned on the right with arrows and pause button */}
          <div
            className="video-controls"
            style={{
              position: "absolute",
              bottom: "20px",
              right: "20px",
              zIndex: 101,
              pointerEvents: "auto",
              display: "flex",
              alignItems: "center",
              gap: "8px",
            }}
          >
            {videoSources.length > 1 && (
              <>
                {/* Previous Arrow */}
                <button
                  className="destination-nav-button prev"
                  onClick={() => {
                    console.log("Previous button clicked");
                    const newIndex =
                      currentDestination === 0
                        ? destinations.length - 1
                        : currentDestination - 1;
                    handleDestinationClick(newIndex);
                  }}
                  aria-label="Previous destination"
                  title="Previous destination"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polyline points="15 18 9 12 15 6"></polyline>
                  </svg>
                </button>

                {/* Pause/Play Button in the center */}
                <button
                  onClick={toggleVideoPlay}
                  className="video-control-button"
                  aria-label={isVideoPlaying ? "Pause video" : "Play video"}
                >
                  {isVideoPlaying ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect x="6" y="4" width="4" height="16"></rect>
                      <rect x="14" y="4" width="4" height="16"></rect>
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polygon points="5 3 19 12 5 21 5 3"></polygon>
                    </svg>
                  )}
                </button>

                {/* Next Arrow */}
                <button
                  className="destination-nav-button next"
                  onClick={() => {
                    console.log("Next button clicked");
                    const newIndex =
                      (currentDestination + 1) % destinations.length;
                    handleDestinationClick(newIndex);
                  }}
                  aria-label="Next destination"
                  title="Next destination"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </button>
              </>
            )}

            {/* If only one video, show just the pause/play button */}
            {videoSources.length === 1 && (
              <button
                onClick={toggleVideoPlay}
                className="video-control-button"
                aria-label={isVideoPlaying ? "Pause video" : "Play video"}
              >
                {isVideoPlaying ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="6" y="4" width="4" height="16"></rect>
                    <rect x="14" y="4" width="4" height="16"></rect>
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                  </svg>
                )}
              </button>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default VideoHeroSection;

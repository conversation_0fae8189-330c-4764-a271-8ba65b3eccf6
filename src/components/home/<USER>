import React, { useState, useEffect } from "react";
import { getHotelsList, getHotelAndRoomTypes } from "../../utils/dataService";
import HotelDetailModal from "../hotels/HotelDetailModal";
import ReactStickyHeader from "../hotels/ReactStickyHeader";
import ReactHotelHero from "../hotels/ReactHotelHero";
import HotelMainContent from "../hotels/HotelMainContent";
import ReactPhotoModal from "../photos/ReactPhotoModal";
import "../../styles/featured-hotels.css";
import "../../styles/hotel-modal.css";

// Define types for our hotel data
interface HotelImage {
  id: string;
  url: string;
  metadata: Record<string, any>;
  rank: number;
  hotel_id: string;
  created_at: string;
  updated_at: string;
  deleted_at: null | string;
}

interface Hotel {
  id: string;
  name: string;
  handle: string;
  description: string;
  rating: number;
  address: string;
  is_featured: boolean;
  is_active: boolean;
  destination_id: string;
  images: HotelImage[];
  lowest_price: number | null;
  highest_price: number | null;
  category_id?: string;
}

const FeaturedHotels: React.FC = () => {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(null);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [hotelDetails, setHotelDetails] = useState<any>(null);
  const [roomTypes, setRoomTypes] = useState<any[]>([]);
  const [detailsLoading, setDetailsLoading] = useState<boolean>(false);
  const [showSplitView, setShowSplitView] = useState<boolean>(false);

  // Function to format price
  const formatPrice = (
    amount: number | null,
    currencyCode: string = "$"
  ): string => {
    if (amount === null) return "";
    return `${currencyCode} ${(amount / 100)?.toLocaleString()}`;
  };

  // Fetch hotels when component mounts
  useEffect(() => {
    const fetchHotels = async () => {
      try {
        setLoading(true);
        const hotelsList = await getHotelsList();
        setHotels(hotelsList as unknown as Hotel[]);
      } catch (err) {
        console.error("Error fetching hotels:", err);
        setError("Failed to load hotels. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchHotels();
  }, []);

  // Handle hotel selection
  const handleHotelSelect = async (hotel: Hotel, event: React.MouseEvent) => {
    event.preventDefault();

    // Set the selected hotel and open modal
    setSelectedHotel(hotel);
    setModalOpen(true);

    // Fetch hotel details and room types
    try {
      setDetailsLoading(true);
      const result = await getHotelAndRoomTypes(hotel.id);
      setHotelDetails(result.hotel);

      // Filter room types to only include available rooms
      const allRoomTypes = result.roomTypes || [];
      const availableRoomTypes = allRoomTypes.filter(
        (room) => room.available !== false
      );
      setRoomTypes(availableRoomTypes);
    } catch (err) {
      console.error("Error fetching hotel details:", err);
    } finally {
      setDetailsLoading(false);
    }
  };

  // Close the modal
  const handleCloseModal = () => {
    setModalOpen(false);
  };

  return (
    <section
      className={`featured-hotels py-8 ${
        showSplitView ? "split-view-active" : ""
      }`}
    >
      {/* Split view container */}
      <div className={`split-view-container ${showSplitView ? "active" : ""}`}>
        {/* Left side - Hotel listing */}
        <div className="split-view-left">
          <div className="section-header mb-4">
            <h2 className="text-2xl font-baskervville">Available Hotels</h2>
          </div>

          {loading && (
            <div className="text-center py-8">
              <p>Loading hotels...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-8 text-red-500">
              <p>{error}</p>
            </div>
          )}

          {!loading && !error && (
            <div className="hotel-grid">
              {hotels.map((hotel) => {
                const isGuestFavorite = hotel.is_featured;
                const isSelected = selectedHotel?.id === hotel.id;

                return (
                  <div
                    key={hotel.id}
                    className={`hotel-card p-2 ${isSelected ? "selected" : ""}`}
                  >
                    <div
                      className="hotel-card-inner"
                      onClick={(e) => handleHotelSelect(hotel, e)}
                    >
                      <div className="hotel-image-container">
                        <img
                          src={
                            hotel.images && hotel.images.length > 0
                              ? hotel.images[0]?.url
                              : "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                          }
                          alt={hotel.name}
                          className="hotel-image"
                        />
                        <button
                          className="favorite-button"
                          aria-label="Add to favorites"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent triggering the card click
                          }}
                        >
                          <svg
                            viewBox="0 0 32 32"
                            xmlns="http://www.w3.org/2000/svg"
                            aria-hidden="true"
                            role="presentation"
                            focusable="false"
                            style={{
                              display: "block",
                              fill: "rgba(0, 0, 0, 0.5)",
                              height: "24px",
                              width: "24px",
                              stroke: "white",
                              strokeWidth: "2",
                              overflow: "visible",
                            }}
                          >
                            <path d="m16 28c7-4.733 14-10 14-17 0-1.792-.683-3.583-2.05-4.95-1.367-1.366-3.158-2.05-4.95-2.05-1.791 0-3.583.684-4.949 2.05l-2.051 2.051-2.05-2.051c-1.367-1.366-3.158-2.05-4.95-2.05-1.791 0-3.583.684-4.949 2.05-1.367 1.367-2.051 3.158-2.051 4.95 0 7 7 12.267 14 17z"></path>
                          </svg>
                        </button>
                        {isGuestFavorite && (
                          <div className="guest-favorite-badge">
                            <span>★</span>
                            <span>Featured</span>
                          </div>
                        )}
                      </div>
                      <div className="hotel-info">
                        <div className="hotel-header">
                          <h3 className="hotel-name">{hotel.name}</h3>
                          <div className="hotel-rating">
                            <span className="star-icon">★</span>
                            <span>{hotel.rating}</span>
                          </div>
                        </div>
                        <p className="hotel-address">{hotel.address || ""}</p>
                        <p className="hotel-price">
                          <span className="price-amount">
                            {formatPrice(hotel.lowest_price)}
                          </span>
                          {hotel.lowest_price && (
                            <span style={{ color: "#717171" }}>
                              {" "}
                              starting price
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Right side - Hotel detail content */}
        {showSplitView && selectedHotel && (
          <div className="split-view-right font-baskervville">
            <div className="hotel-detail-content">
              {detailsLoading ? (
                <div className="flex justify-center items-center h-full">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#285DA6]"></div>
                </div>
              ) : hotelDetails && roomTypes ? (
                <>
                  {/* Sticky Header */}
                  <ReactStickyHeader
                    name={hotelDetails.name}
                    location={hotelDetails.location || ""}
                    rating={hotelDetails.rating}
                  />

                  {/* Hero Section with Gallery */}
                  <ReactHotelHero
                    name={hotelDetails.name}
                    location={hotelDetails.location || ""}
                    rating={hotelDetails.rating}
                    mainImage={hotelDetails.images[0]}
                    images={hotelDetails.images.slice(0, 5)}
                    hotelId={selectedHotel.id}
                  />

                  {/* Main Content */}
                  <HotelMainContent
                    hotelData={hotelDetails}
                    roomTypes={roomTypes}
                    hotelId={selectedHotel.id}
                    nights={4}
                    lang="en"
                  />

                  {/* Photo Modal */}
                  <ReactPhotoModal
                    images={hotelDetails.images}
                    hotelName={hotelDetails.name}
                  />
                </>
              ) : (
                <div className="flex justify-center items-center h-full">
                  <p>Failed to load hotel details. Please try again.</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default FeaturedHotels;

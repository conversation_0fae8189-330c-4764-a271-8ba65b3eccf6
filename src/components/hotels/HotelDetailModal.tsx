import React, { useEffect, useRef, useState } from "react";
import ReactHotelHero from "./ReactHotelHero";
import ReactStickyHeader from "./ReactStickyHeader";
import HotelMainContent from "./HotelMainContent";
import ReactPhotoModal from "../photos/ReactPhotoModal";
import ShareModalController from "../share/ShareModalController";

interface HotelDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  hotelDetails: any;
  roomTypes: any[];
  hotelId: string;
  loading: boolean;
}

const HotelDetailModal: React.FC<HotelDetailModalProps> = ({
  isOpen,
  onClose,
  hotelDetails,
  roomTypes,
  hotelId,
  loading,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [isClosing, setIsClosing] = useState(false);

  // Handle smooth closing animation
  const handleClose = () => {
    setIsClosing(true);
    // Wait for animation to complete before actually closing
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 300); // Match this with the CSS animation duration
  };

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        handleClose();
      }
    };

    // Handle escape key to close
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscKey);
      document.body.style.overflow = "hidden"; // Prevent scrolling of background

      // Hide the header when modal is open - use a more direct approach
      const headerElements = document.querySelectorAll(".header-wrapper");
      headerElements.forEach((header) => {
        (header as HTMLElement).style.display = "none";
      });
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscKey);
      document.body.style.overflow = ""; // Restore scrolling

      // Show the header when modal is closed
      const headerElements = document.querySelectorAll(".header-wrapper");
      headerElements.forEach((header) => {
        (header as HTMLElement).style.display = "";
      });
    };
  }, [isOpen, onClose]);

  // Direct scroll function that can be called from anywhere
  const scrollToSection = (sectionId: string) => {
    if (!modalRef.current) return;

    // Remove the # if it exists
    const id = sectionId.startsWith("#") ? sectionId.substring(1) : sectionId;

    // Find the target element by ID
    const targetElement = document.getElementById(id);
    if (!targetElement) {
      console.error(`Section with id ${id} not found`);
      return;
    }

    // Calculate offset based on target
    let offset = 80; // Default offset

    // Special handling for different sections
    if (id === "booking") {
      offset = 100;
    } else if (id === "rooms") {
      offset = 70;
    }

    // Get the position of the element relative to the modal
    const modalRect = modalRef.current.getBoundingClientRect();
    const elementRect = targetElement.getBoundingClientRect();
    const relativeTop =
      elementRect.top - modalRect.top + modalRef.current.scrollTop;

    // Scroll the modal content
    modalRef.current.scrollTo({
      top: relativeTop - offset,
      behavior: "smooth",
    });
  };

  // Handle anchor links within the modal
  useEffect(() => {
    if (!isOpen || !modalRef.current) return;

    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (
        target.tagName === "A" &&
        target.getAttribute("href")?.startsWith("#")
      ) {
        e.preventDefault();
        const targetId = target.getAttribute("href");
        if (!targetId) return;

        // Use the direct scroll function
        scrollToSection(targetId);
      }
    };

    // Add event listeners to all anchor links in the modal
    const anchorLinks = modalRef.current.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach((link) => {
      link.addEventListener("click", handleAnchorClick as EventListener);
    });

    return () => {
      if (modalRef.current) {
        const anchorLinks = modalRef.current.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach((link) => {
          link.removeEventListener("click", handleAnchorClick as EventListener);
        });
      }
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="hotel-detail-modal-overlay font-baskervville">
      <div
        className={`hotel-detail-modal-container p-4 rounded-r-lg ${
          isClosing ? "closing" : ""
        }`}
      >
        <button
          className="modal-close-button"
          onClick={handleClose}
          aria-label="Close modal"
        >
          ×
        </button>

        <button
          className="mobile-close-button"
          onClick={handleClose}
          aria-label="Close modal"
        >
          ×
        </button>

        <div className="hotel-detail-modal-content" ref={modalRef}>
          {loading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#285DA6]"></div>
            </div>
          ) : hotelDetails && roomTypes ? (
            <>
              {/* Sticky Header */}
              <ReactStickyHeader
                name={hotelDetails.name}
                location={hotelDetails.location || ""}
                rating={hotelDetails.rating}
                onSectionClick={scrollToSection}
              />

              {/* Hero Section with Gallery */}
              <ReactHotelHero
                name={hotelDetails.name}
                location={hotelDetails.location || ""}
                rating={hotelDetails.rating}
                mainImage={hotelDetails.images[0]}
                images={hotelDetails.images.slice(0, 5)}
                isAiSearch={true}
                hotelId={hotelId}
              />

              {/* Main Content */}
              <HotelMainContent
                hotelData={hotelDetails}
                roomTypes={roomTypes}
                hotelId={hotelId}
                nights={4}
                lang="en"
              />

              {/* Photo Modal */}
              <ReactPhotoModal
                images={hotelDetails.images}
                hotelName={hotelDetails.name}
              />

              {/* Share Modal */}
              <ShareModalController />
            </>
          ) : (
            <div className="flex justify-center items-center h-full">
              <p>Failed to load hotel details. Please try again.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HotelDetailModal;

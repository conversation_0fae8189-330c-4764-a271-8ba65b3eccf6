import React, { useState, useEffect } from "react";
import DateRangePicker from "../search/DateRangePicker";
import GuestSelector from "../search/GuestSelector";
import { fetchHotelAvailability } from "../../utils/store/hotels";
import { useIsMobile } from "../../hooks/use-mobile";
import type { Lang } from "../../i18n/ui";
import "../../styles/room-search-filters-mobile.css";

interface RoomSearchFiltersProps {
  hotelId: string;
  checkInDate?: Date | null;
  checkOutDate?: Date | null;
  onDatesChange: (startDate: Date | null, endDate: Date | null) => void;
  guests: {
    adults: number;
    children: number;
    infants: number;
  };
  onGuestChange: (guests: {
    adults: number;
    children: number;
    infants: number;
  }) => void;
  currencyCode: string;
  onAvailabilityUpdate?: (availableRooms: any[]) => void;
  maxAdults?: number;
  maxChildren?: number;
  maxInfants?: number;
  maxOccupancy?: number;
  hotelName: string;
  location: string;
  lang?: Lang;
}

const RoomSearchFilters: React.FC<RoomSearchFiltersProps> = ({
  hotelId,
  checkInDate,
  checkOutDate,
  onDatesChange,
  guests,
  onGuestChange,
  currencyCode,
  onAvailabilityUpdate,
  maxAdults = 4,
  maxChildren = 4,
  maxInfants = 2,
  maxOccupancy = 8,
  hotelName,
  location,
  lang = "en",
}) => {
  const [datePickerOpen, setDatePickerOpen] = useState(false);
  const [guestSelectorOpen, setGuestSelectorOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const isMobile = useIsMobile();

  // Handle body scroll lock for mobile modal
  useEffect(() => {
    if (isMobile && (datePickerOpen || guestSelectorOpen)) {
      // Prevent body scroll when modal is open
      document.body.style.overflow = "hidden";
    } else {
      // Restore body scroll when modal is closed
      document.body.style.overflow = "unset";
    }

    // Cleanup function to restore scroll on unmount
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isMobile, datePickerOpen, guestSelectorOpen]);

  // Handle keyboard events for mobile modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isMobile) {
        if (datePickerOpen) {
          setDatePickerOpen(false);
        }
        if (guestSelectorOpen) {
          setGuestSelectorOpen(false);
        }
      }
    };

    if (isMobile && (datePickerOpen || guestSelectorOpen)) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isMobile, datePickerOpen, guestSelectorOpen]);

  // Format dates for display
  const formatDateForDisplay = (date: Date | null | undefined) => {
    if (!date) return "";
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Unified function to update availability
  const updateAvailability = async (params: {
    startDate?: Date | null;
    endDate?: Date | null;
    newGuests?: typeof guests;
  }) => {
    // Determine which dates to use
    const startDate =
      params.startDate !== undefined ? params.startDate : checkInDate;
    const endDate =
      params.endDate !== undefined ? params.endDate : checkOutDate;

    // Determine which guest counts to use
    const currentGuests = params.newGuests || guests;

    // If both dates are selected, fetch availability
    if (startDate && endDate) {
      try {
        setIsLoading(true);
        const availabilityData = await fetchHotelAvailability(
          hotelId,
          startDate,
          endDate,
          currentGuests.adults,
          currentGuests.children,
          currentGuests.infants,
          currencyCode
        );

        // Update available rooms if callback is provided
        if (onAvailabilityUpdate && availabilityData.available_rooms) {
          onAvailabilityUpdate(availabilityData.available_rooms);
        }
      } catch (error) {
        console.error("Error fetching hotel availability:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Handle date range changes
  const handleDateRangeChange = async (
    startDate: Date | null,
    endDate: Date | null
  ) => {
    // Update parent component with new dates
    onDatesChange(startDate, endDate);

    // Update availability with new dates
    await updateAvailability({ startDate, endDate });
  };

  // Handle guest changes
  const handleGuestChange = async (newGuests: typeof guests) => {
    // Update parent component with new guest count
    onGuestChange(newGuests);

    // Update availability with new guest count
    await updateAvailability({ newGuests });
  };

  return (
    <div className="mb-8 bg-white border border-[#3566ab]/10 rounded-xl shadow-md">
      <div className="p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <div>
            <h2 className="text-xl font-baskervville text-[#3566ab] mb-1">
              Your Stay at {hotelName}
            </h2>
            <p className="text-sm text-foreground/60">
              Click on dates or guests to modify your search
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-[#3566ab]/5 p-4 rounded-lg">
            <div className="flex items-center mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#3566ab] mr-2"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              <span className="text-sm font-medium">Location</span>
            </div>
            <p className="text-foreground/80">{location}</p>
          </div>

          {/* Date Card */}
          <button
            type="button"
            onClick={() => {
              setDatePickerOpen(!datePickerOpen);
              setGuestSelectorOpen(false);
            }}
            className="bg-[#3566ab]/5 p-4 rounded-lg relative border-2 border-transparent hover:border-[#3566ab]/20 transition-all duration-200 cursor-pointer group w-full text-left hover:bg-[#3566ab]/10"
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab] mr-2"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                <span className="text-sm font-medium">Dates</span>
              </div>
              <div className="flex items-center text-[#3566ab]/60 group-hover:text-[#3566ab] transition-colors">
                <span className="text-xs mr-1">Change</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M7 7h10v10"></path>
                  <path d="M7 17 17 7"></path>
                </svg>
              </div>
            </div>

            <div className="group-hover:text-[#3566ab] transition-colors">
              <p className="text-foreground/80 font-medium">
                {checkInDate && checkOutDate
                  ? `${formatDateForDisplay(
                      checkInDate
                    )} - ${formatDateForDisplay(checkOutDate)}`
                  : "Select dates"}
              </p>
              {checkInDate && checkOutDate && (
                <p className="text-xs text-foreground/60 mt-1">
                  {Math.ceil(
                    (checkOutDate.getTime() - checkInDate.getTime()) /
                      (1000 * 60 * 60 * 24)
                  )}{" "}
                  nights
                </p>
              )}
            </div>

            {/* Date Range Picker - Mobile Modal or Desktop Popover */}
            {datePickerOpen && (
              <>
                {isMobile ? (
                  // Mobile Modal
                  <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm mobile-modal-backdrop">
                    <div
                      className="relative bg-white rounded-xl shadow-xl border border-[#3566ab]/10 w-[90vw] max-w-[350px] max-h-[90vh] overflow-y-auto m-4 mobile-modal-enter"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {/* Close button for mobile */}
                      <button
                        type="button"
                        onClick={() => setDatePickerOpen(false)}
                        className="absolute top-4 right-4 z-10 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-gray-600"
                        >
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                      </button>
                      <DateRangePicker
                        startDate={checkInDate}
                        endDate={checkOutDate}
                        onDateRangeChange={handleDateRangeChange}
                        onClose={() => setDatePickerOpen(false)}
                        disableClickOutside={true}
                        onlyCloseOnBothDates={true}
                        lang={lang}
                      />
                    </div>
                  </div>
                ) : (
                  // Desktop Popover
                  <div
                    className="absolute z-50 top-full left-0 mt-2 bg-white rounded-xl shadow-xl border border-[#3566ab]/10 w-auto mmt-date-picker-popover"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <DateRangePicker
                      startDate={checkInDate}
                      endDate={checkOutDate}
                      onDateRangeChange={handleDateRangeChange}
                      onClose={() => setDatePickerOpen(false)}
                      disableClickOutside={true}
                      onlyCloseOnBothDates={true}
                      lang={lang}
                    />
                  </div>
                )}
              </>
            )}
          </button>

          {/* Guest Card */}
          <button
            type="button"
            onClick={() => {
              setGuestSelectorOpen(!guestSelectorOpen);
              setDatePickerOpen(false);
            }}
            className="bg-[#3566ab]/5 p-4 rounded-lg relative border-2 border-transparent hover:border-[#3566ab]/20 transition-all duration-200 cursor-pointer group w-full text-left hover:bg-[#3566ab]/10"
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab] mr-2"
                >
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
                <span className="text-sm font-medium">Guests</span>
              </div>
              <div className="flex items-center text-[#3566ab]/60 group-hover:text-[#3566ab] transition-colors">
                <span className="text-xs mr-1">Change</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M7 7h10v10"></path>
                  <path d="M7 17 17 7"></path>
                </svg>
              </div>
            </div>

            <div className="group-hover:text-[#3566ab] transition-colors">
              <p className="text-foreground/80 font-medium">
                {guests.adults + guests.children + guests.infants}{" "}
                {guests.adults + guests.children + guests.infants === 1
                  ? "Guest"
                  : "Guests"}
              </p>
              <p className="text-xs text-foreground/60 mt-1">
                {guests.adults} {guests.adults === 1 ? "Adult" : "Adults"}
                {guests.children > 0
                  ? `, ${guests.children} ${
                      guests.children === 1 ? "Child" : "Children"
                    }`
                  : ""}
                {guests.infants > 0
                  ? `, ${guests.infants} ${
                      guests.infants === 1 ? "Infant" : "Infants"
                    }`
                  : ""}
              </p>
            </div>

            {/* Guest Selector - Mobile Modal or Desktop Popover */}
            {guestSelectorOpen && (
              <>
                {isMobile ? (
                  // Mobile Modal
                  <div
                    className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm mobile-modal-backdrop"
                    onClick={(e) => {
                      if (e.target === e.currentTarget) {
                        setGuestSelectorOpen(false);
                      }
                    }}
                  >
                    <div className="relative bg-white rounded-xl shadow-xl border border-[#3566ab]/10 w-[90vw] max-w-[350px] max-h-[90vh] overflow-y-auto m-4 mobile-modal-enter">
                      {/* Close button for mobile */}
                      <button
                        type="button"
                        onClick={() => setGuestSelectorOpen(false)}
                        className="absolute top-4 right-4 z-10 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-gray-600"
                        >
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                      </button>
                      <GuestSelector
                        adults={guests.adults}
                        children={guests.children}
                        infants={guests.infants}
                        onGuestChange={handleGuestChange}
                        maxAdults={maxAdults}
                        maxChildren={maxChildren}
                        maxInfants={maxInfants}
                        maxOccupancy={maxOccupancy}
                        isOpen={guestSelectorOpen}
                        onClose={() => setGuestSelectorOpen(false)}
                        isMobileModal={true}
                        lang={lang}
                      />
                    </div>
                  </div>
                ) : (
                  // Desktop Popover
                  <div className="absolute z-50 top-full right-0 mt-2 bg-white rounded-xl shadow-xl border border-[#3566ab]/10 w-auto">
                    <GuestSelector
                      adults={guests.adults}
                      children={guests.children}
                      infants={guests.infants}
                      onGuestChange={handleGuestChange}
                      maxAdults={maxAdults}
                      maxChildren={maxChildren}
                      maxInfants={maxInfants}
                      maxOccupancy={maxOccupancy}
                      isOpen={guestSelectorOpen}
                      onClose={() => setGuestSelectorOpen(false)}
                      isMobileModal={false}
                      lang={lang}
                    />
                  </div>
                )}
              </>
            )}
          </button>
        </div>

        {/* Loading Indicator */}
        {isLoading && (
          <div className="mt-4 flex items-center justify-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#3566ab]"></div>
            <span className="ml-2 text-sm text-[#3566ab]/80">
              Checking availability...
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default RoomSearchFilters;

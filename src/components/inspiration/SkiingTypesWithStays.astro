---
import { getHotelsBySkiingType } from "../../utils/dataService";
import { inspirationCategories } from "../../utils/inspirationData";
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";

interface Props {
  lang: Lang;
}

const { lang } = Astro.props;
const t = useTranslations(lang);

// Use the centralized inspiration data with translations
const skiingTypes = inspirationCategories.map(category => ({
  id: category.id,
  name: t(`inspiration.${category.id}.name`),
  category: t(`inspiration.${category.id}.category`),
  tag: category.tag, // Keep unchanged for filtering
  imageUrl: category.imageUrl,
  description: t(`inspiration.${category.id}.description`),
  searchPrompt: category.searchPrompt, // Keep unchanged for backend
}));

// Fetch hotels for each skiing type
const skiingTypesWithHotels = await Promise.all(
  skiingTypes.map(async (type) => {
    try {
      const hotels = await getHotelsBySkiingType(type.tag, 3); // Get 3 hotels per type
      return {
        ...type,
        hotels: hotels || [],
      };
    } catch (error) {
      console.error(`Error fetching hotels for ${type.name}:`, error);
      return {
        ...type,
        hotels: [],
      };
    }
  })
);
---

<section id="skiing-experiences" class="py-20 lg:py-32 bg-gradient-to-b from-white to-gray-50/30">
  <div class="container-custom">
    <!-- Section Header -->
    <div class="text-center mb-16 lg:mb-24">
      <div class="inline-flex items-center justify-center w-16 h-16 bg-[#285DA6]/10 rounded-full mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#285DA6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
          <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
        </svg>
      </div>
      <span class="text-sm uppercase tracking-[0.2em] text-[#285DA6] font-karla font-medium">
        {t("inspiration.section.title")}
      </span>
      <h2 class="text-4xl md:text-5xl lg:text-6xl font-baskervville uppercase tracking-[0.05em] mt-6 mb-8 leading-tight">
        Discover Your Skiing Style
      </h2>
      <p class="font-karla text-lg md:text-xl max-w-4xl mx-auto text-foreground/70 leading-relaxed">
        {t("inspiration.section.subtitle")}
      </p>
    </div>

    <!-- Skiing Types with Featured Stays -->
    <div class="space-y-24 lg:space-y-32">
      {skiingTypesWithHotels.map((type, index) => (
        <div class={`skiing-type-section group ${index % 2 === 1 ? 'reverse-layout' : ''}`}>
          <div class="bg-white rounded-3xl shadow-xl shadow-gray-900/5 overflow-hidden border border-gray-100/50">
            <div class="grid grid-cols-1 lg:grid-cols-2 lg:min-h-[500px]">
              <!-- Image Side -->
              <div class={`image-side relative ${index % 2 === 1 ? 'lg:order-2' : ''}`}>
                <div class="relative h-80 lg:h-full overflow-hidden">
                  <img
                    src={type.imageUrl}
                    alt={type.name}
                    class="w-full h-full object-cover object-center"
                    style="aspect-ratio: 4/3;"
                  />
                  <div class="absolute inset-0 bg-gradient-to-br from-black/20 via-transparent to-black/60"></div>

                  <!-- Floating Badge -->
                  <div class="absolute top-6 left-6">
                    <div class="bg-white/95 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg">
                      <span class="text-xs font-karla font-medium text-[#285DA6] uppercase tracking-wider">
                        {type.category}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Content Side -->
              <div class={`content-side p-8 lg:p-12 flex flex-col justify-center ${index % 2 === 1 ? 'lg:order-1' : ''}`}>
                <div class="mb-8">
                  <h3 class="text-3xl md:text-4xl font-baskervville uppercase tracking-[0.02em] mb-6 text-gray-900 leading-tight">
                    {type.name}
                  </h3>
                  <p class="font-karla text-lg text-gray-600 leading-relaxed mb-8">
                    {type.description}
                  </p>

                  <!-- CTA Button -->
                  <a
                    href={`/ai-search?query=${encodeURIComponent(type.searchPrompt)}`}
                    class="inline-flex items-center group/btn bg-[#285DA6] text-white px-6 py-3 rounded-full font-karla font-medium text-sm uppercase tracking-wider transition-all duration-300 hover:bg-[#1e4a8c] hover:shadow-lg hover:shadow-[#285DA6]/25 hover:-translate-y-0.5"
                  >
                    {t("inspiration.skiingTypes.explore")}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="ml-2 transition-transform duration-300 group-hover/btn:translate-x-1"
                    >
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                      <polyline points="12 5 19 12 12 19"></polyline>
                    </svg>
                  </a>
                </div>

                <!-- Recommended Stays for this type -->
                {type.hotels.length > 0 && (
                  <div class="featured-stays border-t border-gray-100 pt-8">
                    <div class="flex items-center justify-between mb-6">
                      <h4 class="text-xl font-karla font-semibold text-gray-900">
                        {t("inspiration.skiingTypes.recommendedStays")}
                      </h4>
                      <span class="text-sm text-gray-500 font-karla">
                        {type.hotels.length === 1 ? `1 ${t("inspiration.skiingTypes.property")}` : `${type.hotels.length} ${t("inspiration.skiingTypes.properties")}`}
                      </span>
                    </div>

                    <!-- Carousel Container -->
                    <div class="relative">
                      <!-- Navigation Arrows -->
                      <button
                        class="carousel-prev absolute -left-3 top-1/2 -translate-y-1/2 z-10 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full shadow-md border border-gray-200/50 flex items-center justify-center text-gray-600 hover:text-[#285DA6] hover:bg-white transition-all duration-300 hover:shadow-lg hover:scale-105 md:hidden"
                        onclick="scrollCarousel(this, 'prev')"
                        aria-label="Previous hotels"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                      </button>

                      <button
                        class="carousel-next absolute -right-3 top-1/2 -translate-y-1/2 z-10 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full shadow-md border border-gray-200/50 flex items-center justify-center text-gray-600 hover:text-[#285DA6] hover:bg-white transition-all duration-300 hover:shadow-lg hover:scale-105 md:hidden"
                        onclick="scrollCarousel(this, 'next')"
                        aria-label="Next hotels"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <polyline points="9 6 15 12 9 18"></polyline>
                        </svg>
                      </button>

                      <!-- Scroll Container -->
                      <div class="carousel-container flex gap-4 overflow-x-auto scrollbar-hide pb-2 snap-x snap-mandatory px-10 md:px-0" style="scroll-behavior: smooth;">
                        {type.hotels.slice(0, 6).map((hotel) => (
                          <div class="flex-shrink-0 w-72 sm:w-80 snap-start">
                            <div class="group/hotel bg-gray-50/50 rounded-xl p-4 transition-all duration-300 hover:bg-white hover:shadow-md border border-transparent hover:border-gray-200">
                              <div class="flex items-center space-x-4">
                                <div class="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                                  <img
                                    src={hotel.imageUrl}
                                    alt={hotel.name}
                                    class="w-full h-full object-cover object-center transition-transform duration-300 group-hover/hotel:scale-110"
                                    style="aspect-ratio: 1/1;"
                                  />
                                </div>
                                <div class="flex-1 min-w-0">
                                  <h5 class="font-karla font-medium text-gray-900 truncate group-hover/hotel:text-[#285DA6] transition-colors">
                                    {hotel.name}
                                  </h5>
                                  <p class="text-sm text-gray-500 truncate">
                                    {hotel.location}
                                  </p>
                                </div>
                                <div class="flex-shrink-0">
                                  <a
                                    href={`/stays/${hotel.id}`}
                                    class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-[#285DA6]/10 text-[#285DA6] opacity-0 group-hover/hotel:opacity-100 transition-all duration-300 hover:bg-[#285DA6] hover:text-white"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                      <line x1="7" y1="17" x2="17" y2="7"></line>
                                      <polyline points="7 7 17 7 17 17"></polyline>
                                    </svg>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}

                        <!-- Show More Card (if there are more than 6 hotels) -->
                        {type.hotels.length > 6 && (
                          <div class="flex-shrink-0 w-72 sm:w-80 snap-start">
                            <div class="bg-gradient-to-br from-[#285DA6]/5 to-[#285DA6]/10 rounded-xl p-4 h-full flex items-center justify-center border-2 border-dashed border-[#285DA6]/20 transition-all duration-300 hover:border-[#285DA6]/40 hover:bg-[#285DA6]/5">
                              <div class="text-center">
                                <div class="w-12 h-12 bg-[#285DA6]/10 rounded-full flex items-center justify-center mx-auto mb-3">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#285DA6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                  </svg>
                                </div>
                                <p class="text-sm font-karla font-medium text-[#285DA6] mb-1">
                                  +{type.hotels.length - 6} {t("inspiration.skiingTypes.more")}
                                </p>
                                <p class="text-xs text-gray-500">
                                  {t("inspiration.skiingTypes.viewAllProperties")}
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      <!-- Scroll Indicators -->
                      {type.hotels.length > 2 && (
                        <div class="flex justify-center mt-4 space-x-2">
                          {Array.from({ length: Math.min(Math.ceil(type.hotels.length / 2), 4) }).map(() => (
                            <div class="w-2 h-2 rounded-full bg-gray-300 transition-colors duration-300"></div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>

    <!-- Call to Action Section -->
    <div class="text-center mt-20 lg:mt-24">
      <div class="bg-gradient-to-r from-[#285DA6] to-[#1e4a8c] rounded-3xl p-12 lg:p-16 text-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
          <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>

        <div class="relative z-10 max-w-3xl mx-auto">
          <h3 class="text-3xl md:text-4xl font-baskervville uppercase tracking-[0.02em] mb-6">
            {t("inspiration.skiingTypes.readyToDiscover")}
          </h3>
          <p class="text-lg md:text-xl text-white/90 mb-8 leading-relaxed">
            {t("inspiration.skiingTypes.exploreComplete")}
          </p>

          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a
              href="/destinations"
              class="group inline-flex items-center bg-white text-[#285DA6] px-8 py-4 rounded-full font-karla font-semibold text-sm uppercase tracking-wider transition-all duration-300 hover:bg-gray-50 hover:shadow-lg hover:-translate-y-1"
            >
              {t("inspiration.skiingTypes.exploreAllDestinations")}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2 transition-transform duration-300 group-hover:translate-x-1"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>

            <a
              href="/stays"
              class="group inline-flex items-center border-2 border-white/30 text-white px-8 py-4 rounded-full font-karla font-semibold text-sm uppercase tracking-wider transition-all duration-300 hover:bg-white/10 hover:border-white/50"
            >
              {t("inspiration.skiingTypes.viewAllStays")}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-2 transition-transform duration-300 group-hover:translate-x-1"
              >
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  .skiing-type-section {
    position: relative;
  }

  /* Smooth scroll behavior */
  @media (prefers-reduced-motion: no-preference) {
    .skiing-type-section {
      scroll-margin-top: 2rem;
    }
  }

  /* Enhanced hover effects */
  .skiing-type-section .group:hover .image-side img {
    transform: scale(1.05);
  }

  /* Consistent image sizing and aspect ratios */
  .image-side img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
  }

  /* Ensure consistent dimensions for main images */
  .image-side > div {
    aspect-ratio: 4/3;
  }

  /* Full height image on desktop */
  @media (min-width: 1024px) {
    .image-side > div {
      aspect-ratio: unset;
      height: 100%;
    }

    .image-side {
      display: flex;
      flex-direction: column;
    }

    .image-side > div {
      flex: 1;
    }
  }

  /* Hotel card image consistency */
  .group\/hotel img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    aspect-ratio: 1/1;
  }

  /* Fallback for browsers that don't support aspect-ratio */
  @supports not (aspect-ratio: 4/3) {
    .image-side > div {
      padding-bottom: 75%; /* 4:3 aspect ratio */
      position: relative;
    }

    .image-side img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  @supports not (aspect-ratio: 1/1) {
    .group\/hotel .w-16.h-16 {
      position: relative;
      padding-bottom: 100%; /* 1:1 aspect ratio */
      height: 0;
    }

    .group\/hotel .w-16.h-16 img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  /* Staggered animation for hotel cards */
  .featured-stays .group\/hotel:nth-child(1) {
    animation-delay: 0ms;
  }

  .featured-stays .group\/hotel:nth-child(2) {
    animation-delay: 100ms;
  }

  .featured-stays .group\/hotel:nth-child(3) {
    animation-delay: 200ms;
  }

  /* Subtle entrance animation */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .skiing-type-section {
    animation: fadeInUp 0.6s ease-out;
  }

  /* Hide scrollbar for carousel */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Smooth scrolling for carousel */
  .scrollbar-hide {
    scroll-behavior: smooth;
  }

  /* Snap scrolling for better UX */
  .snap-x {
    scroll-snap-type: x mandatory;
  }

  .snap-start {
    scroll-snap-align: start;
  }

  /* Custom scrollbar for other elements */
  .featured-stays::-webkit-scrollbar {
    width: 4px;
  }

  .featured-stays::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
  }

  .featured-stays::-webkit-scrollbar-thumb {
    background: #285DA6;
    border-radius: 2px;
  }

  .featured-stays::-webkit-scrollbar-thumb:hover {
    background: #1e4a8c;
  }

  /* Carousel card animations */
  .featured-stays .flex-shrink-0 {
    transition: transform 0.2s ease;
  }

  .featured-stays .flex-shrink-0:hover {
    transform: translateY(-2px);
  }

  /* Carousel navigation arrows */
  .carousel-prev,
  .carousel-next {
    opacity: 0.8;
    transition: all 0.3s ease;
  }

  .carousel-prev:hover,
  .carousel-next:hover {
    opacity: 1;
    transform: translateY(-50%) scale(1.05);
  }

  .carousel-prev:active,
  .carousel-next:active {
    transform: translateY(-50%) scale(0.95);
  }

  /* Hide arrows on desktop where scroll is preferred */
  @media (min-width: 768px) {
    .carousel-prev,
    .carousel-next {
      display: none;
    }
  }

  /* Image loading states */
  .image-side img,
  .group\/hotel img {
    background-color: #f3f4f6;
    transition: opacity 0.3s ease;
  }

  .image-side img[src=""],
  .group\/hotel img[src=""] {
    opacity: 0;
  }
</style>

<script>
  // Carousel navigation function
  function scrollCarousel(button, direction) {
    const carouselContainer = button.parentElement.querySelector('.carousel-container');
    const cardWidth = carouselContainer.querySelector('.flex-shrink-0').offsetWidth;
    const gap = 16; // 4 * 4px (gap-4)
    const scrollAmount = cardWidth + gap;

    if (direction === 'next') {
      carouselContainer.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    } else {
      carouselContainer.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
    }

    // Update arrow visibility based on scroll position
    updateArrowVisibility(carouselContainer);
  }

  // Update arrow visibility based on scroll position
  function updateArrowVisibility(container) {
    const prevButton = container.parentElement.querySelector('.carousel-prev');
    const nextButton = container.parentElement.querySelector('.carousel-next');

    const isAtStart = container.scrollLeft <= 0;
    const isAtEnd = container.scrollLeft >= container.scrollWidth - container.clientWidth - 1;

    if (prevButton) {
      prevButton.style.opacity = isAtStart ? '0.3' : '0.8';
      prevButton.style.pointerEvents = isAtStart ? 'none' : 'auto';
    }

    if (nextButton) {
      nextButton.style.opacity = isAtEnd ? '0.3' : '0.8';
      nextButton.style.pointerEvents = isAtEnd ? 'none' : 'auto';
    }
  }

  // Initialize carousel arrow states when page loads
  document.addEventListener('DOMContentLoaded', function() {
    const carouselContainers = document.querySelectorAll('.carousel-container');

    carouselContainers.forEach(container => {
      // Initial arrow state
      updateArrowVisibility(container);

      // Update arrows on scroll
      container.addEventListener('scroll', () => {
        updateArrowVisibility(container);
      });

      // Update arrows on resize
      window.addEventListener('resize', () => {
        updateArrowVisibility(container);
      });
    });
  });

  // Make function globally available
  window.scrollCarousel = scrollCarousel;
</script>

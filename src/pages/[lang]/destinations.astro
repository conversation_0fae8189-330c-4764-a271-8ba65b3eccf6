---
import Layout from "../../components/layout/Layout.astro";
import { getAllDestinations } from "../../utils/dataService";
import DestinationHero from "../../components/destinations/DestinationHero.astro";
import CategorySection from "../../components/destinations/CategorySection.astro";
import DestinationFeatures from "../../components/destinations/DestinationFeatures.astro";
import AllDestinations from "../../components/destinations/AllDestinations.astro";
import CTASection from "../../components/home/<USER>";

import {
  getLangFromUrl,
  useTranslations,
  getStaticPathsForLocales,
} from "../../i18n/utils";

// Generate static paths for non-default languages
export function getStaticPaths() {
  return getStaticPathsForLocales();
}

// Get language from URL and set up translations
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Fetch destinations from API
const destinations = await getAllDestinations();

// Define page metadata
const title = `${t("destinations.hero.title")} - Perfect Piste`;
const description = t("destinations.hero.subtitle");
---

<Layout title={title} description={description}>
  <!-- Hero Section -->
  <DestinationHero
    title={t("destinations.hero.title")}
    subtitle={t("destinations.hero.subtitle")}
    lang={lang}
  />

  <!-- Category Section -->
  <CategorySection lang={lang} />

  <!-- Destination Features -->
  <DestinationFeatures
    title={t("destinations.features.title")}
    lang={lang}
  />

  <!-- All Destinations -->
  <AllDestinations
    destinations={destinations}
    title={t("destinations.all.title")}
    lang={lang}
  />

  <!-- CTA Section -->
  <CTASection
    title={t("destinations.cta.title")}
    description={t("destinations.cta.description")}
    ctaText={t("destinations.cta.button")}
    ctaLink="/ai-search"
    backgroundImage="https://images.unsplash.com/photo-1486870591958-9b9d0d1dda99?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80"
  />
</Layout>

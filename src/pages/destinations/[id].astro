---
import Layout from "../../components/layout/Layout.astro";
import {
  getHotelsByDestination,
  getDestinationById,
  getAllDestinations,
} from "../../utils/dataService";
import DestinationDetailHero from "../../components/destinations/DestinationDetailHero.astro";
import DestinationExperience from "../../components/destinations/DestinationExperience.astro";
import DestinationHotels from "../../components/destinations/DestinationHotels.astro";
import DestinationTips from "../../components/destinations/DestinationTips.astro";
import DestinationFAQ from "../../components/destinations/DestinationFAQ.astro";
import DestinationCTA from "../../components/destinations/DestinationCTA.astro";

// Define the getStaticPaths function to generate all possible destination pages
export async function getStaticPaths() {
  const destinations = await getAllDestinations();
  return destinations.map((destination) => ({
    params: { id: destination.id },
    props: { destinationId: destination.id },
  }));
}

// Get the destination ID from the URL
const { id } = Astro.params;

// Fetch the destination data
const destination = await getDestinationById(id);

// Fallback destination data in case API fails
const fallbackDestination = {
  id: id,
  name: "Beautiful Destination",
  description: "A wonderful place to visit",
  propertyCount: 10,
  imageUrl: "https://images.unsplash.com/photo-1530122037265-a5f1f91d3b99",
  activities: ["Skiing", "Hiking", "Relaxing"],
  country: "Switzerland",
  faqs: [],
};

// Use destination data or fallback
const destinationData = destination || fallbackDestination;

// Get hotels for this destination
const hotels = await getHotelsByDestination(id);

// Define page metadata
const title = `${destinationData.name} - Perfect Piste`;
const description = destinationData.description;
---

<Layout title={title} description={description}>
  <!-- Hero Section -->
  <DestinationDetailHero
    name={destinationData.name}
    propertyCount={destinationData.propertyCount}
    imageUrl={destinationData.imageUrl}
  />

  <!-- Experience Section -->
  <DestinationExperience
    name={destinationData.name}
    description={destinationData.description}
    activities={destinationData.activities || []}
    imageUrl={destinationData.imageUrl}
  />

  <!-- Top Hotels Section -->
  <DestinationHotels destinationName={destinationData.name} hotels={hotels} />

  <!-- Travel Tips -->
  <DestinationTips destinationName={destinationData.name} />

  <!-- FAQ Section -->
  <DestinationFAQ
    destinationName={destinationData.name}
    faqs={destinationData.faqs || []}
  />

  <!-- CTA Section -->
  <DestinationCTA destinationName={destinationData.name} />
</Layout>

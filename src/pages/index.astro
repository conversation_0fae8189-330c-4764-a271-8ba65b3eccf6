---
import Layout from "../components/layout/Layout.astro";
import VideoHeroSection from "../components/home/<USER>";
import CategorySection from "../components/home/<USER>";
import FeaturedStaysSection from "../components/home/<USER>";

import CTASection from "../components/home/<USER>";
import WhyPerfectPisteSection from "../components/home/<USER>";
import PerfectPisteAmenitiesSection from "../components/home/<USER>";

import { useTranslations } from "../i18n/utils";
import { defaultLang } from "../i18n/ui";

// Get translations for default language (English)
const t = useTranslations(defaultLang);

// Define video sources for different destinations
const videoSources = [
  {
    src: "/videos/hero/video-1.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/video-2.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-1.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-2.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-3.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-4.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-5.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-6.mp4",
    type: "video/mp4",
  },
];

// Define ski destinations to showcase
const skiDestinations = [
  "Zermatt",
  "St. Moritz",
  "Verbier",
  "Courchevel",
  "Aspen",
];
---

<Layout title={t("home.hero.title")} description={t("home.hero.description")}>
  <main>
    <VideoHeroSection
      client:load
      title={t("home.hero.mainTitle")}
      subtitle={t("home.hero.subtitle")}
      videoSources={videoSources}
      destinations={skiDestinations}
    />
    <div class="py-16 mt-16">
      <WhyPerfectPisteSection lang={defaultLang} />
    </div>
    <div class="py-16">
      <CategorySection lang={defaultLang} />
    </div>
    <div class="py-16" id="how-to-use-ai">
      <PerfectPisteAmenitiesSection lang={defaultLang} />
    </div>
    <div class="py-16">
      <FeaturedStaysSection lang={defaultLang} />
    </div>
    <!-- <div class="py-16">
      <WebStoriesSection />
    </div> -->
  </main>

  <div class="pt-16 pb-32">
    <CTASection
      title={t("home.cta.title")}
      description={t("home.cta.description")}
      ctaText={t("home.cta.primaryButton")}
      ctaLink="/ai-search"
    />
  </div>
</Layout>

import { getHeaders } from "./common";

/**
 * Interface for traveler information
 */
export interface Traveler {
  name: string;
  age?: number;
}

/**
 * Interface for travelers data
 */
export interface TravelersData {
  adults: Traveler[];
  children: Traveler[];
  infants: Traveler[];
}

/**
 * Interface for add-on selection in cart
 */
export interface CartAddOn {
  service_id: string;
  adult_quantity: number;
  child_quantity: number;
}

/**
 * Interface for cart creation parameters
 */
export interface CreateCartParams {
  hotel_id: string;
  room_config_id: string;
  // room_id?: string;
  check_in_date: string;
  check_out_date: string;
  check_in_time?: string;
  check_out_time?: string;
  guest_name?: string;
  guest_email?: string;
  guest_phone?: string;
  adults: number;
  children: number;
  infants: number;
  travelers?: TravelersData;
  number_of_rooms: number;
  total_amount: number;
  currency_code: string;
  region_id?: string;
  special_requests?: string;
  notes?: string;
  metadata?: Record<string, any>;
  add_ons?: CartAddOn[];
  shipping_address?: {
    first_name: string;
    last_name: string;
    address_1: string;
    city: string;
    country_code: string;
    postal_code: string;
    phone: string;
  };
}

/**
 * Interface for cart response
 */
export interface CartResponse {
  cart: {
    id: string;
    hotel_id: string;
    room_config_id: string;
    room_id: string;
    check_in_date: string;
    check_out_date: string;
    check_in_time: string;
    check_out_time: string;
    guest_name: string;
    guest_email: string;
    guest_phone: string;
    adults: number;
    children: number;
    infants: number;
    number_of_rooms: number;
    total_amount: number;
    currency_code: string;
    region_id: string;
    special_requests: string;
    notes: string;
    metadata: Record<string, any>;
    created_at: string;
    updated_at: string;
  };
}

/**
 * Interface for payment session response
 */
export interface PaymentSessionResponse {
  payment_session: {
    id: string;
    cart_id: string;
    provider_id: string;
    data: {
      client_secret: string;
      [key: string]: any;
    };
    status: string;
    created_at: string;
    updated_at: string;
  };
}

/**
 * Interface for complete cart response
 */
export interface CompleteCartResponse {
  booking?: {
    id: string;
    status: string;
    payment_status: string;
    [key: string]: any;
  };
  order?: {
    id: string;
    [key: string]: any;
  };
  success?: boolean;
}

/**
 * Create a hotel booking cart
 * @param params - The cart creation parameters
 * @returns A promise that resolves to the cart response
 */
export async function createHotelCart(
  params: CreateCartParams
): Promise<CartResponse> {
  try {
    // Add a region_id if not provided - using environment variable or a more generic approach
    if (!params.region_id) {
      // Try to get region ID from environment variable or use a fallback approach
      params.region_id =
        import.meta.env.DEFAULT_REGION_ID ||
        params.hotel_id.split("_")[0] ||
        "default";
    }

    const response = await fetch(
      `${import.meta.env.PUBLIC_BACKEND_URL}/store/hotel-management/cart`,
      {
        method: "POST",
        headers: getHeaders(),
        body: JSON.stringify(params),
      }
    );

    if (!response.ok) {
      let errorMessage = `Failed to create cart: ${response.status} ${response.statusText}`;

      try {
        const errorData = await response.json();
        // Add more detailed error information if available
        if (errorData.message) {
          errorMessage += ` - ${errorData.message}`;
        }
        if (errorData.error) {
          errorMessage += ` - ${errorData.error}`;
        }
      } catch (jsonError) {
        // Could not parse error response as JSON
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();

    // Store cart ID in session storage for later use
    if (typeof window !== "undefined" && data.cart?.id) {
      sessionStorage.setItem("hotel_cart_id", data.cart.id);
    }

    return data;
  } catch (error) {
    // Error in createHotelCart

    throw error;
  }
}

/**
 * Create a payment session for a cart
 * @param cartId - The cart ID
 * @param paymentProviderId - The payment provider ID (e.g., "pp_stripe_stripe")
 * @returns A promise that resolves to the payment session response
 */
export async function createCartPaymentSession(
  cartId: string,
  paymentProviderId: string = "pp_stripe_stripe"
): Promise<PaymentSessionResponse> {
  try {
    const requestBody = {
      cart_id: cartId,
      payment_provider_id: paymentProviderId,
    };

    const response = await fetch(
      `${
        import.meta.env.PUBLIC_BACKEND_URL
      }/store/hotel-management/cart/payment`,
      {
        method: "POST",
        headers: getHeaders(),
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      let errorMessage = `Failed to create payment session: ${response.status} ${response.statusText}`;

      try {
        const errorData = await response.json();

        // Add more detailed error information if available
        if (errorData.message) {
          errorMessage += ` - ${errorData.message}`;
        }
        if (errorData.error) {
          errorMessage += ` - ${errorData.error}`;
        }
      } catch (jsonError) {
        // Could not parse error response as JSON
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();

    // Store payment session ID and client secret in session storage
    if (typeof window !== "undefined" && data.payment_session?.id) {
      sessionStorage.setItem(
        "stripe_payment_session_id",
        data.payment_session.id
      );

      if (data.payment_session.data?.client_secret) {
        sessionStorage.setItem(
          "stripe_client_secret",
          data.payment_session.data.client_secret
        );
      }
    }

    return data;
  } catch (error) {
    // Error in createCartPaymentSession

    throw error;
  }
}

/**
 * Update a payment session with additional data
 * @param cartId - The cart ID
 * @param paymentSessionId - The payment session ID
 * @param data - The data to update
 * @returns A promise that resolves to the updated payment session
 */
export async function updateCartPaymentSession(
  cartId: string,
  paymentSessionId: string,
  data: Record<string, any>
): Promise<PaymentSessionResponse> {
  try {
    const response = await fetch(
      `${
        import.meta.env.PUBLIC_BACKEND_URL
      }/store/hotel-management/cart/payment`,
      {
        method: "PUT",
        headers: getHeaders(),
        body: JSON.stringify({
          cart_id: cartId,
          payment_session_id: paymentSessionId,
          data,
        }),
      }
    );

    if (!response.ok) {
      await response.json(); // Read the response body but don't use it

      throw new Error(
        `Failed to update payment session: ${response.status} ${response.statusText}`
      );
    }

    const responseData = await response.json();
    return responseData;
  } catch (error) {
    // Error in updateCartPaymentSession
    throw error;
  }
}

/**
 * Complete a cart and convert it to an order
 * @param cartId - The cart ID
 * @param roomId - The room ID
 * @param checkInDate - The check-in date
 * @param checkOutDate - The check-out date
 * @param sessionId - The Stripe session ID from the payment confirmation
 * @returns A promise that resolves to the complete cart response
 */
export async function completeCart(
  cartId: string,
  roomId: string,
  checkInDate: string,
  checkOutDate: string,
  sessionId: string
): Promise<CompleteCartResponse> {
  try {
    const requestBody = {
      cart_id: cartId,
      room_id: roomId,
      check_in_date: checkInDate,
      check_out_date: checkOutDate,
      session_id: sessionId,
    };

    console.log("Completing cart with data:", requestBody, getHeaders());

    const response = await fetch(
      `${import.meta.env.PUBLIC_BACKEND_URL}/store/checkout/complete`,
      {
        method: "POST",
        headers: getHeaders(),
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      let errorMessage = `Failed to complete cart: ${response.status} ${response.statusText}`;

      try {
        const errorData = await response.json();

        // Add more detailed error information if available
        if (errorData.message) {
          errorMessage += ` - ${errorData.message}`;
        }
        if (errorData.error) {
          errorMessage += ` - ${errorData.error}`;
        }
      } catch (jsonError) {
        // Could not parse error response as JSON
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();

    // Clear cart-related session storage
    if (typeof window !== "undefined") {
      sessionStorage.removeItem("hotel_cart_id");
      sessionStorage.removeItem("stripe_payment_session_id");
      sessionStorage.removeItem("stripe_client_secret");
    }

    return data;
  } catch (error) {
    // Error in completeCart

    throw error;
  }
}

/**
 * Capture a payment for a completed booking
 * @param paymentSessionId - The payment session ID
 * @returns A promise that resolves to the capture response
 */
export async function capturePayment(paymentSessionId: string): Promise<any> {
  try {
    const response = await fetch(
      `${
        import.meta.env.PUBLIC_BACKEND_URL
      }/store/hotel-management/bookings/payment/capture`,
      {
        method: "POST",
        headers: getHeaders(),
        body: JSON.stringify({
          payment_session_id: paymentSessionId,
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error("[CART API] Error capturing payment:", errorData);
      throw new Error(
        `Failed to capture payment: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("[CART API] Error in capturePayment:", error);
    throw error;
  }
}

/**
 * Create a Stripe Checkout session for a cart
 * @param cartId - The cart ID
 * @returns A promise that resolves to the checkout session response
 */
export async function createCheckoutSession(cartId: string): Promise<any> {
  try {
    const successUrl = `${window.location.origin}/booking-confirmation?session_id={CHECKOUT_SESSION_ID}`;
    const cancelUrl = `${window.location.origin}/review-booking`;

    console.log("Creating checkout session with URLs:", {
      successUrl,
      cancelUrl,
      currentOrigin: window.location.origin,
    });

    const response = await fetch(
      `${import.meta.env.PUBLIC_BACKEND_URL}/store/checkout/sessions`,
      {
        method: "POST",
        headers: getHeaders(),
        body: JSON.stringify({
          cart_id: cartId,
          success_url: successUrl,
          cancel_url: cancelUrl,
        }),
      }
    );

    if (!response.ok) {
      let errorMessage = `Failed to create checkout session: ${response.status} ${response.statusText}`;

      try {
        const errorData = await response.json();
        if (errorData.message) {
          errorMessage += ` - ${errorData.message}`;
        }
        if (errorData.error) {
          errorMessage += ` - ${errorData.error}`;
        }
      } catch (jsonError) {
        // Could not parse error response as JSON
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();
    console.log("Checkout session response:", data);

    // Return the data with normalized property name for consistency
    return {
      ...data,
      url: data.checkout_url || data.url, // Support both property names
    };
  } catch (error) {
    console.error("[CART API] Error in createCheckoutSession:", error);
    throw error;
  }
}

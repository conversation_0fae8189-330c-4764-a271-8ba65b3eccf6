/**
 * Utility functions for fetching hotel data from the backend API
 */

import { formatDateForAPI } from "../dateUtils";

// Types for hotel data
export interface HotelImage {
  id?: string;
  url: string;
  alt?: string;
  isThumbnail?: boolean;
  rank?: number;
}

export interface CancellationPolicy {
  id: string;
  name: string;
  description: string;
  days_before_checkin: number;
  refund_type: string;
  refund_amount: number;
}

export interface RoomDetails {
  room_size: string;
  bed_type: string;
  max_adults: number;
  max_children: number;
  max_infants: number;
  max_occupancy: number;
  max_extra_beds: number;
}

export interface MealPlanPrice {
  amount: number;
  original_amount: number;
  currency_code: string;
  total_amount: number;
  per_night_amount: number;
  nights: number;
}

export interface RoomPrice {
  amount: number;
  original_amount: number;
  currency_code: string;
  total_amount: number;
  per_night_amount: number;
  nights: number;
  tiers?: any;
  meal_plans?: {
    none?: MealPlanPrice;
    bb?: MealPlanPrice;
    hb?: MealPlanPrice;
    fb?: MealPlanPrice;
  };
  selected_meal_plan?: string;
}

export interface RoomConfiguration {
  id: string;
  title: string;
  subtitle?: string;
  description: string;
  handle: string;
  thumbnail?: string;
  status: string;
  room_details?: RoomDetails;
  // Keep these for backward compatibility
  room_size?: string;
  bed_type?: string;
  max_adults?: number;
  max_children?: number;
  max_infants?: number;
  max_occupancy?: number;
  max_extra_beds?: number;
  amenities: string[];
  images: HotelImage[];
  available_rooms_count?: number;
  // Availability data
  available?: boolean;
  available_rooms?: number;
  price?: RoomPrice;
}

export interface HotelRoom {
  id: number | string;
  name: string;
  description: string;
  price: number;
  size: string;
  image: string | HotelImage;
  perks: string[];
}

export interface HotelAddOn {
  id: string;
  name: string;
  description: string;
  service_type: string;
  service_level: string;
  adult_price: number;
  child_price: number;
  currency_code: string;
  max_capacity: number;
  images: string[];
  hotel_id: string[];
  hotel_name: string[];
  destination_id: string | null;
  destination_name: string | null;
}

export interface Hotel {
  id: number | string;
  name: string;
  description: string;
  location: string;
  stars: number;
  rating: number;
  reviews: number;
  price: number;
  images: string[] | HotelImage[];
  amenities: string[];
  rules: string[];
  rooms?: HotelRoom[];
  handle?: string;
  is_active?: boolean;
  website?: string;
  email?: string;
  destination_id?: string;
  notes?: string;
  address?: string;
  phone_number?: string;
  timezone?: string;
  available_languages?: string[];
  tax_type?: string;
  tax_number?: string;
  tags?: string[];
  currency?: string;
  check_in_time?: string;
  check_out_time?: string;
  is_featured?: boolean;
  category_id?: string;
  created_at?: string;
  updated_at?: string;
  safety_measures?: string[];
  cancellation_policies?: CancellationPolicy[];
  room_configurations?: RoomConfiguration[];
}

// Common headers for API requests
const getHeaders = () => ({
  "Content-Type": "application/json",
  "x-publishable-api-key": import.meta.env.PUBLIC_BACKEND_API_KEY || "",
});

/**
 * Fetch a single hotel by its slug
 * @param slug - The hotel slug/handle
 * @param defaultHotel - Optional default hotel data to use if API fails
 * @returns The hotel data
 */
export async function fetchHotelBySlug(
  slug: string,
  defaultHotel?: Hotel
): Promise<Hotel> {
  try {
    const response = await fetch(
      `${
        import.meta.env.PUBLIC_BACKEND_URL
      }/store/hotel-management/hotels/${slug}`,
      {
        method: "GET",
        headers: getHeaders(),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data?.hotel?.[0]) {
      throw new Error("Hotel data not found");
    }

    // Map API data to our hotel structure
    const hotelData = data.hotel[0];
    return {
      id: hotelData.id,
      name: hotelData.name || defaultHotel?.name || "",
      description: hotelData.description || defaultHotel?.description || "",
      location: hotelData.location || defaultHotel?.location || "",
      stars: hotelData.stars || defaultHotel?.stars || 0,
      rating: hotelData.rating || defaultHotel?.rating || 0,
      reviews: hotelData.reviews || defaultHotel?.reviews || 0,
      price: hotelData.price || defaultHotel?.price || 0,
      images:
        hotelData.images?.length > 0
          ? hotelData.images.map((img: any) => img.url as string)
          : defaultHotel?.images || [],
      amenities: hotelData.amenities || defaultHotel?.amenities || [],
      safety_measures:
        hotelData.safety_measures || defaultHotel?.safety_measures || [],
      rooms:
        hotelData.rooms?.length > 0
          ? hotelData.rooms.map((room: any) => ({
              id: room.id,
              name: room.name,
              description: room.description,
              price: room.price,
              size: room.size,
              image: room.image?.url || defaultHotel?.rooms?.[0]?.image || "",
              perks: room.perks || [],
            }))
          : defaultHotel?.rooms || [],
      handle: hotelData.handle || slug,
    };
  } catch (e) {
    console.error("Error fetching hotel:", e);
    // Return default hotel data if provided and API fails
    if (defaultHotel) {
      return defaultHotel;
    }
    throw e;
  }
}

/**
 * Fetch hotel details by ID
 * @param id - The hotel ID
 * @returns The detailed hotel data including room configurations and cancellation policies
 */
export async function fetchHotelDetailsById(id: string): Promise<{
  hotel: Hotel;
  room_configurations: RoomConfiguration[];
}> {
  try {
    const response = await fetch(
      `${
        import.meta.env.PUBLIC_BACKEND_URL
      }/store/hotel-management/hotels/${id}/details`,
      {
        method: "GET",
        headers: getHeaders(),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data?.hotel) {
      throw new Error("Hotel details not found");
    }

    return {
      hotel: data.hotel,
      room_configurations: data.room_configurations || [],
    };
  } catch (e) {
    console.error("Error fetching hotel details:", e);
    throw e;
  }
}

/**
 * Fetch hotels by skiing type/category tags
 * @param skiingType - The skiing type to filter by (e.g., 'family-skiing', 'off-piste-skiing', 'luxury-skiing', 'beginner-skiing')
 * @param limit - Number of hotels to fetch
 * @returns The hotels data filtered by skiing type
 */
export async function fetchHotelsBySkiingType(
  skiingType: string,
  limit: number = 6
): Promise<{ hotels: Hotel[]; count: number }> {
  try {
    // Build query parameters directly
    const queryParams = new URLSearchParams();
    queryParams.append("limit", limit.toString());
    queryParams.append("is_active", "true");
    queryParams.append("is_featured", "true"); // Focus on featured hotels for inspiration
    queryParams.append("tags", skiingType); // Add the skiing type tag

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/hotels?${queryParams.toString()}`;

    console.log(`Fetching hotels for skiing type: ${skiingType}`);
    console.log(`API URL: ${url}`);

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return {
      hotels: data?.hotels || [],
      count: data?.count || 0,
    };
  } catch (e) {
    console.error(`Error fetching hotels for skiing type ${skiingType}:`, e);
    return { hotels: [], count: 0 };
  }
}

/**
 * Fetch multiple hotels with optional parameters
 * @param options - Options for fetching hotels
 * @returns The hotels data and total count
 */
export async function fetchHotels(options: {
  limit?: number;
  offset?: number;
  isFeatured?: boolean;
  searchParams?: any;
  destinationId?: string | number;
  tags?: string[];
}): Promise<{ hotels: Hotel[]; count: number }> {
  try {
    const {
      limit = 10,
      offset = 0,
      isFeatured,
      searchParams,
      destinationId,
      tags,
    } = options;

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append("limit", limit.toString());
    queryParams.append("offset", offset.toString());
    queryParams.append("is_active", "true");

    if (isFeatured) {
      queryParams.append("is_featured", "true");
    }

    // Add tags filter if provided directly
    if (tags && tags.length > 0) {
      tags.forEach((tag) => {
        queryParams.append("tags", tag);
      });
    }

    if (searchParams) {
      // Handle tags from searchParams
      if (searchParams.tags && Array.isArray(searchParams.tags)) {
        searchParams.tags.forEach((tag: string) => {
          queryParams.append("tags", tag);
        });
      }

      // Filter out empty string values from searchParams
      const filteredParams: Record<string, any> = {};

      for (const [key, value] of Object.entries(searchParams)) {
        // Only include values that are not empty strings and not undefined
        if (value !== "" && value !== undefined) {
          filteredParams[key] = value;
        }
      }

      // Handle location search separately if provided and not empty and not undefined
      if (
        searchParams.location &&
        searchParams.location !== "" &&
        searchParams.location !== undefined
      ) {
        queryParams.append("location", searchParams.location);
      }

      // Only append searchParams if there are non-empty values
      if (Object.keys(filteredParams).length > 0) {
        queryParams.append("searchParams", JSON.stringify(filteredParams));
      }
    }

    if (destinationId && destinationId !== "") {
      queryParams.append("destination_id", destinationId.toString());
    }

    console.log("queryParams", queryParams.toString());

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/hotels?${queryParams.toString()}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Check if the data structure is different than expected
    if (!data.hotels && data.data && data.data.hotels) {
      return {
        hotels: data.data.hotels || [],
        count: data.data.count || 0,
      };
    }

    return {
      hotels: data?.hotels || [],
      count: data?.count || 0,
    };
  } catch (e) {
    console.error("Error fetching hotels:", e);
    return { hotels: [], count: 0 };
  }
}

/**
 * Fetch hotels from the list endpoint
 * @returns The hotels list data
 */
export async function fetchHotelsList(): Promise<{ hotels: Hotel[] }> {
  try {
    const response = await fetch(
      `${
        import.meta.env.PUBLIC_BACKEND_URL
      }/store/hotel-management/hotels/list`,
      {
        method: "GET",
        headers: {
          ...getHeaders(),
          "x-publishable-api-key": import.meta.env.PUBLIC_BACKEND_API_KEY,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return {
      hotels: data?.hotels || [],
    };
  } catch (e) {
    console.error("Error fetching hotels list:", e);
    return { hotels: [] };
  }
}

/**
 * Fetch featured hotels
 * @param limit - Maximum number of hotels to fetch
 * @returns The featured hotels
 */
export async function fetchFeaturedHotels(limit: number = 3): Promise<Hotel[]> {
  try {
    const { hotels } = await fetchHotels({ limit, isFeatured: true });
    return hotels;
  } catch (e) {
    console.error("Error fetching featured hotels:", e);
    return [];
  }
}

/**
 * Fetch hotels by destination
 * @param destinationSlug - The destination slug
 * @param limit - Maximum number of hotels to fetch
 * @returns The hotels for the specified destination
 */
/**
 * Fetch hotel availability and pricing information
 * @param hotelId - The hotel ID
 * @param checkIn - Check-in date (YYYY-MM-DD)
 * @param checkOut - Check-out date (YYYY-MM-DD)
 * @param adults - Number of adults
 * @param children - Number of children
 * @param infants - Number of infants
 * @param currencyCode - Currency code (e.g., USD)
 * @returns The hotel availability data including available rooms and pricing
 */
export async function fetchHotelAvailability(
  hotelId: string,
  checkIn: Date | string,
  checkOut: Date | string,
  adults: number = 2,
  children: number = 0,
  infants: number = 0,
  currencyCode: string = "USD",
  isPetsAllowed?: boolean
): Promise<{
  hotel: Hotel;
  check_in: string;
  check_out: string;
  nights: number;
  adults: string;
  children: string;
  infants: string;
  currency_code: string;
  available_rooms: RoomConfiguration[];
}> {
  try {
    // Format dates if they are Date objects using ISO format (YYYY-MM-DD) as required by the API
    const formattedCheckIn =
      typeof checkIn === "string" ? checkIn : formatDateForAPI(checkIn);
    const formattedCheckOut =
      typeof checkOut === "string" ? checkOut : formatDateForAPI(checkOut);

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append("check_in", formattedCheckIn);
    queryParams.append("check_out", formattedCheckOut);
    queryParams.append("adults", adults.toString());
    queryParams.append("children", children.toString());
    queryParams.append("infants", infants.toString());
    queryParams.append("currency_code", currencyCode.toLowerCase());
    queryParams.append("country_code", "gb");

    // Add pets parameter if provided
    if (isPetsAllowed) {
      queryParams.append("is_pets_allowed", "true");
    }

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/hotels/${hotelId}/availability?${queryParams.toString()}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (e) {
    console.error("Error fetching hotel availability:", e);
    throw e;
  }
}

export async function fetchHotelsByDestination(
  destinationSlug: string,
  limit: number = 10,
  isFeaturedHotels: boolean
): Promise<{ hotels: Hotel[]; destination: any }> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append("limit", limit.toString());
    queryParams.append("is_active", "true");

    if (isFeaturedHotels) {
      queryParams.append("is_featured", "true");
    }

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/destinations/${destinationSlug}?${queryParams.toString()}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Check if the data structure is different than expected
    if (!data.hotels && data.data && data.data.hotels) {
      return {
        hotels: data.data.hotels || [],
        destination: data.data.destination?.[0] || null,
      };
    }

    const destination = data?.destination?.[0] || null;
    const hotels = data?.hotels || [];

    return {
      hotels,
      destination,
    };
  } catch (e) {
    console.error("Error fetching hotels by destination:", e);
    return { hotels: [], destination: null };
  }
}

/**
 * Fetch hotels availability for search results
 * @param checkIn - Check-in date (YYYY-MM-DD)
 * @param checkOut - Check-out date (YYYY-MM-DD)
 * @param adults - Number of adults
 * @param children - Number of children
 * @param infants - Number of infants
 * @param currencyCode - Currency code (e.g., USD)
 * @returns The hotels availability data
 */
export async function fetchHotelsAvailability(
  checkIn: Date | string,
  checkOut: Date | string,
  adults: number = 2,
  children: number = 0,
  infants: number = 0,
  currencyCode: string = "usd",
  destinationId?: string,
  isPetsAllowed?: boolean
): Promise<{
  hotels: Hotel[];
  check_in: string;
  check_out: string;
  nights: number;
  adults: string;
  children: string;
  infants: string;
  currency_code: string;
  count: number;
}> {
  try {
    // Format dates if they are Date objects using ISO format (YYYY-MM-DD) as required by the API
    const formattedCheckIn =
      typeof checkIn === "string" ? checkIn : formatDateForAPI(checkIn);
    const formattedCheckOut =
      typeof checkOut === "string" ? checkOut : formatDateForAPI(checkOut);

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append("check_in", formattedCheckIn);
    queryParams.append("check_out", formattedCheckOut);
    queryParams.append("adults", adults.toString());
    queryParams.append("children", children.toString());
    queryParams.append("infants", infants.toString());
    queryParams.append("currency_code", currencyCode.toLowerCase());

    // Add destination_id if provided
    if (destinationId) {
      queryParams.append("destination_id", destinationId);
    }

    // Add pets parameter if provided
    if (isPetsAllowed) {
      queryParams.append("is_pets_allowed", "true");
    }

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/hotels/availability?${queryParams.toString()}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (e) {
    console.error("Error fetching hotels availability:", e);
    return {
      hotels: [],
      check_in:
        typeof checkIn === "string" ? checkIn : formatDateForAPI(checkIn),
      check_out:
        typeof checkOut === "string" ? checkOut : formatDateForAPI(checkOut),
      nights: 0,
      adults: adults.toString(),
      children: children.toString(),
      infants: infants.toString(),
      currency_code: currencyCode,
      count: 0,
    };
  }
}

/**
 * Fetch hotel add-ons/services
 * @param hotelId - The hotel ID
 * @returns The hotel add-ons data
 */
export async function fetchHotelAddOns(hotelId: string): Promise<{
  add_ons: HotelAddOn[];
}> {
  try {
    const response = await fetch(
      `${
        import.meta.env.PUBLIC_BACKEND_URL
      }/store/hotel-management/hotels/${hotelId}/add-ons`,
      {
        method: "GET",
        headers: getHeaders(),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      add_ons: data?.add_ons || [],
    };
  } catch (e) {
    console.error("Error fetching hotel add-ons:", e);
    return {
      add_ons: [],
    };
  }
}
